"use client"

import { useMemo } from "react"
import { AlertCircle } from "lucide-react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"

type Trek = {
    id: number
    name: string
    days: number
    type?: string
    oldPrice?: number
    price: number
    departFrom: string // ISO date
    departTo: string   // ISO date
    status: "Guaranteed" | "Limited" | "Open"
    link: string
}

const TREKS: Trek[] = [
    {
        id: 1,
        name: "Dhorpatan Trek",
        days: 7,
        type: "trekking",
        oldPrice: 980,
        price: 880,
        departFrom: "2025-10-12",
        departTo: "2025-10-18",
        status: "Guaranteed",
        link: "dhorpatan-trek"
    },
    {
        id: 2,
        name: "Annapurna Sanctuary Trail Run",
        days: 10,
        type: "trail-running",
        oldPrice: 1300,
        price: 1200,
        departFrom: "2025-10-23",
        departTo: "2025-11-01",
        status: "Guaranteed",
        link: "annapurna-sanctuary-trail-run"
    },
    {
        id: 3,
        name: "Manaslu Circuit Trekking Nepal",
        days: 11,
        type: "trekking",
        oldPrice: 1300,
        price: 1040,
        departFrom: "2025-11-06",
        departTo: "2025-11-16",
        status: "Guaranteed",
        link: "manaslu-circuit-trek"
    },
    {
        id: 4,
        name: "Thorong Peak Climbing",
        days: 16,
        oldPrice: 900,
        price: 640,
        departFrom: "2025-11-21",
        departTo: "2025-12-06",
        status: "Guaranteed",
        link: "thorong-peak-climbing"
    },
]

export default function UpcomingTreksSection() {
    const rows = useMemo(() => TREKS, [])

    return (
        <section className="py-8 md:py-16 bg-white">
            <div className="container mx-auto px-4">
                <h2 className="md:text-4xl text-3xl font-bold text-gray-900 text-center mb-12 tracking-wide">
                    Upcoming Events
                </h2>

                {/* Solo note */}
                <div className="bg-secondary/10 text-dark rounded-lg p-6 flex gap-4 mb-10">
                    <AlertCircle className="shrink-0 w-6 h-6 mt-1 md:block hidden" />
                    <div className="text-sm md:text-base">
                        <p className="font-semibold mb-1">Note to solo travelers:</p>
                        <p>
                            If you are a solo traveler, please contact us via Call/WhatsApp/Viber at{" "}
                            <a href="tel:+9779856016904" className="font-semibold underline">
                                +977 9856016904
                            </a>{" "}
                            or email{" "}
                            <a href="mailto:<EMAIL>" className="font-semibold underline">
                                <EMAIL>
                            </a>{" "}
                            before booking to confirm the departure date.
                        </p>
                    </div>
                </div>

                {/* Table */}
                <div className="hidden md:block cursor-pointer">
                    <div className="grid grid-cols-12 text-xs font-semibold text-gray-500 uppercase tracking-wider pb-2 border-b">
                        <div className="col-span-5">Trip Name</div>
                        <div className="col-span-2">Price</div>
                        <div className="col-span-3">Departure Date</div>
                        <div className="col-span-1">Trip Status</div>
                        <div className="col-span-1" />
                    </div>

                    {rows.map((t) => {
                        const save = t.oldPrice ? t.oldPrice - t.price : 0
                        return (
                            <div
                                key={t.id}
                                className="grid grid-cols-12 py-6 border-b items-center"
                            >
                                {/* name */}
                                <div className="col-span-5">
                                    <p className="font-medium text-lg text-drak">
                                        {t.name} - {t.days} Day{t.days > 1 && "s"}
                                    </p>
                                </div>

                                {/* price */}
                                <div className="col-span-2 flex items-center gap-2">
                                    {t.oldPrice && (
                                        <span className="text-gray-400 line-through text-sm">
                                            {t.oldPrice}
                                        </span>
                                    )}
                                    <span className="font-semibold text-drak">{t.price}</span>
                                    {save > 0 && (
                                        <span className="bg-red text-white text-[10px] px-2 py-0.5 rounded">
                                            Save USD {save}
                                        </span>
                                    )}
                                </div>

                                {/* dates */}
                                <div className="col-span-3 text-sm">
                                    <p className="font-semibold">{t.days} Days</p>
                                    <p className="text-slate-600">
                                        {t.departFrom} - {t.departTo}
                                    </p>
                                </div>

                                {/* status */}
                                <div className="col-span-1 text-sm text-slate-600">
                                    {t.status}
                                </div>

                                {/* button */}
                                <div className="col-span-1 flex justify-end">
                                    <Link
                                        href={`/${t.type}/${t.link}`}
                                    >
                                        <Button className="px-4 py-2 rounded-full border text-white text-sm transition">
                                            Book Now
                                        </Button>
                                    </Link>
                                </div>
                            </div>
                        )
                    })}
                </div>

                {/* Mobile cards */}
                <div className="md:hidden space-y-6 cursor-pointer">
                    {rows.map((t) => {
                        const save = t.oldPrice ? t.oldPrice - t.price : 0
                        return (
                            <div key={t.id} className="border rounded-lg p-4 shadow-sm">
                                <p className="font-semibold text-base text-drak mb-1">
                                    {t.name} - {t.days} Day{t.days > 1 && "s"}
                                </p>
                                <div className="flex items-center gap-2 mb-2">
                                    {t.oldPrice && (
                                        <span className="text-gray-400 line-through text-xs">
                                            {t.oldPrice}
                                        </span>
                                    )}
                                    <span className="font-semibold text-drak">{t.price}</span>
                                    {save > 0 && (
                                        <span className="bg-red text-white text-[10px] px-2 py-0.5 rounded">
                                            Save USD {save}
                                        </span>
                                    )}
                                </div>
                                <p className="text-xs text-slate-600 mb-1">
                                    <span className="font-semibold">{t.days} Days</span> — {t.departFrom} to{" "}
                                    {t.departTo}
                                </p>
                                <p className="text-xs text-slate-600 mb-3">Status: {t.status}</p>
                                <Link
                                    href={`/book/${t.id}`}
                                >
                                    <Button className="px-4 py-2 rounded-full border text-white text-sm transition">
                                        Book Now
                                    </Button>
                                </Link>
                            </div>
                        )
                    })}
                </div>
            </div>
        </section>
    )
}
