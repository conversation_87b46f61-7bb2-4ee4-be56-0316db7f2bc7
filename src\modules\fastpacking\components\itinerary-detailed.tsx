'use client';

import { List, Mountain } from 'lucide-react';
import type React from 'react';
import type { JSX } from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import Image from 'next/image';
import HtmlContentDisplay from '@/components/html-content-display';

export interface DayDetail {
  day: string;
  title: string;
  stats: { icon: JSX.Element; label: string; value: string }[];
  progressPct: number;
  description?: React.ReactNode;
  altitudes?: { label: string; value: string }[];
  image?: string;
}

interface ItineraryDetailedProps {
  days: DayDetail[];
}

const ItineraryDetailed: React.FC<ItineraryDetailedProps> = ({ days }) => {
  const StatItem = ({
    icon,
    label,
    value,
  }: {
    icon: JSX.Element;
    label: string;
    value: string;
  }) => (
    <div className="flex items-center gap-2 text-sm text-dark">
      <div className="text-brand">{icon}</div>
      <span>
        {label}: <span className="font-semibold text-foreground">{value}</span>
      </span>
    </div>
  );

  const AltitudeGrid = ({
    altitudes,
  }: {
    altitudes: { label: string; value: string }[];
  }) => (
    <Card className="bg-brand/2 border-brand/20">
      <CardHeader className="pb-0 pt-0">
        <div className="flex items-center gap-2">
          <Mountain className="h-4 w-4 text-brand" />
          <h4 className="font-semibold text-brand">Altitude Information</h4>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="grid grid-cols-1 sm:grid-cols-3 lg:grid-cols-3 gap-3">
          {altitudes.map((altitude, index) => (
            <div
              key={index}
              className="flex justify- items-center p-0 bg-background/50 rounded-md"
            >
              <span className="text-sm font-medium text-dark">
                {altitude.label}:
              </span>
              <Badge variant="secondary" className="text-xs">
                {altitude.value}
              </Badge>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );

  return (
    <section className="space-y-6">
      <div className="flex items-center gap-3">
        <div className="w-10 h-10 bg-brand rounded-full flex items-center justify-center">
          <List size={18} className="text-white" />
        </div>
        <h2 className="text-2xl md:text-3xl font-bold text-brand">
          Detailed Trek Itinerary
        </h2>
      </div>

      <Accordion type="multiple" className="w-full">
        {days
          .sort(
            (a, b) =>
              parseInt(a.day.split('-')[0].trim()) -
              parseInt(b.day.split('-')[0].trim())
          )
          .map((dayDetail) => {
            const { day, title, stats, progressPct, description, altitudes } =
              dayDetail;

            return (
              <AccordionItem
                key={day}
                value={`day-${day}`}
                className="border rounded-lg mb-2"
              >
                {/* Accordion Header */}
                <AccordionTrigger className="px-4 py-3 text-left">
                  <div className="flex items-center gap-4 w-full">
                    <div
                      className={`flex items-center justify-center bg-brand text-white rounded-md h-8 flex-shrink-0 ${'w-14 px-3'}`}
                    >
                      <span className="text-sm font-bold leading-none whitespace-nowrap">
                        {day}
                      </span>
                    </div>
                    <h3 className="text-lg font-semibold text-dark">{title}</h3>
                  </div>
                </AccordionTrigger>

                {/* Accordion Content */}
                <AccordionContent>
                  <Card className="border-dashed mt-2">
                    <CardContent className="space-y-2 pt-4">
                      {/* Stats */}
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                        {stats.map((stat, idx) => (
                          <StatItem
                            key={idx}
                            icon={stat.icon}
                            label={stat.label}
                            value={stat.value}
                          />
                        ))}
                      </div>

                      {/* Trek Progress */}
                      <div className="space-y-2">
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-base text-dark">
                            Trek Progress
                          </span>
                          <span className="font-semibold text-brand">
                            {progressPct}%
                          </span>
                        </div>
                        <Progress value={progressPct} className="h-2" />
                      </div>

                      {description && (
                        <HtmlContentDisplay
                          htmlContent={description as string}
                          className="prose"
                        />
                      )}

                      {/* Optional Altitude Table */}
                      {altitudes && <AltitudeGrid altitudes={altitudes} />}

                      {dayDetail.image && (
                        <div className="w-full relative mb-4">
                          <Image
                            src={dayDetail.image}
                            alt={`Image for day ${dayDetail.day} - ${dayDetail.title}`}
                            width={1200}
                            height={800}
                            className="object-cover rounded-lg"
                            priority={false}
                          />
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </AccordionContent>
              </AccordionItem>
            );
          })}
      </Accordion>
    </section>
  );
};

export default ItineraryDetailed;
