'use client';
import * as React from "react";
import { Di<PERSON>, DialogTrigger, DialogContent, DialogTitle } from "@/components/ui/dialog";

const regions = [
    {
        id: 1,
        title: "Annapurna Region",
        mapUrl: "https://www.google.com/maps/d/embed?mid=1YtT6dqFt0JovoXbADstqJ8LBTZ3wbtw&ehbc=2E312F",
    },
    {
        id: 2,
        title: "Manaslu Trial Run",
        mapUrl: "https://www.google.com/maps/d/embed?mid=1Iyvf5IshTd8uTXmASSD2L82qbO_XSaU&ehbc=2E312F&noprof=1",
    },
    {
        id: 3,
        title: "Mansalu Circuit Trail",
        mapUrl: "https://www.gpsvisualizer.com/display/map/20251007004943-49156-map.html?center=28.60502,84.402466&zoom=9",
    },
];

export default function RegionSection() {
    return (
        <section className="py-8 md:py-16 bg-gray-50">
            <div className="container mx-auto px-4 max-w-6xl">
                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 text-center mb-16">
                    Hiking Area
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    {regions.map((region) => (
                        <Dialog key={region.id}>
                            <DialogTrigger asChild>
                                <div className="group block relative overflow-hidden rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer">
                                    <div className="relative h-72 md:h-96 overflow-hidden">
                                        <div className="absolute inset-0" style={{ top: '-68px', height: 'calc(100% + 48px)' }}>
                                            <iframe
                                                src={region.mapUrl}
                                                width="100%"
                                                height="100%"
                                                style={{ border: 0 }}
                                                allowFullScreen
                                                loading="lazy"
                                                referrerPolicy="no-referrer-when-downgrade"
                                                className="w-full h-full"
                                            />
                                        </div>
                                        <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent" />
                                    </div>
                                    <div className="absolute bottom-0 left-0 right-0 p-6 text-white">
                                        <h3 className="text-xl font-bold mb-2 group-hover:text-secondary transition-colors">
                                            {region.title}
                                        </h3>
                                    </div>
                                </div>
                            </DialogTrigger>
                            <DialogContent className="!max-w-[900px] h-[90vh] flex flex-col p-0 overflow-hidden">
                                <DialogTitle className="block md:sr-only">{region.title}</DialogTitle>
                                <iframe
                                    src={region.mapUrl}
                                    width="100%"
                                    height="100%"
                                    style={{ border: 0, minHeight: '70vh' }}
                                    allowFullScreen
                                    loading="lazy"
                                    referrerPolicy="no-referrer-when-downgrade"
                                    className="w-full h-full"
                                />
                            </DialogContent>
                        </Dialog>
                    ))}
                </div>
            </div>
        </section>
    );
}


// 'use client';
// import * as React from 'react';

// export default function RegionSection() {
//     return (
//         <section className="py-8 md:py-16 bg-gray-50">
//             <div className="container mx-auto px-4 max-w-6xl">
//                 <h2 className="text-3xl md:text-4xl font-bold text-gray-900 text-center mb-16">Hiking Area</h2>
//                 <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
//                     <div className="group block relative overflow-hidden rounded-xl shadow-lg hover:shadow-xl transition-all duration-300">
//                         <div className="relative h-72 md:h-96 overflow-hidden">
//                             {/* <div className="absolute inset-0" style={{ top: '-68px', height: 'calc(100% + 48px)' }}> */}
//                                 <iframe
//                                     src="https://www.google.com/maps/d/embed?mid=1YtT6dqFt0JovoXbADstqJ8LBTZ3wbtw&ehbc=2E312F"
//                                     width="100%"
//                                     height="100%"
//                                     style={{ border: 0 }}
//                                     allowFullScreen
//                                     loading="lazy"
//                                     referrerPolicy="no-referrer-when-downgrade"
//                                     className="w-full h-full"
//                                 ></iframe>
//                             {/* </div> */}
//                             <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent pointer-events-none" />
//                         </div>
//                         <div className="absolute bottom-0 left-0 right-0 p-6 text-white pointer-events-none">
//                             <h3 className="text-xl font-bold mb-2 group-hover:text-secondary transition-colors">
//                                 Annapurna Region
//                             </h3>
//                             <p className="text-gray-200 text-sm leading-relaxed">
//                                 Iconic trek through diverse landscapes
//                             </p>
//                         </div>
//                     </div>
//                 </div>
//             </div>
//         </section>
//     );
// }