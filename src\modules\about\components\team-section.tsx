'use client';

import { useState, useEffect } from 'react';
import getTeams from '@/actions/teams/get-all-teams';
import { Button } from '@/components/ui/button';
import Image from 'next/image';
import { ITeam, ITeamMember } from '@/types/team';
import HtmlContentDisplay from '@/components/html-content-display';

// shadcn carousel
import {
    Carousel,
    CarouselContent,
    CarouselItem,
    CarouselNext,
    CarouselPrevious,
} from "@/components/ui/carousel";

export default function OurTeamSection() {
    const [team, setTeam] = useState<null | ITeam>(null);
    const [selected, setSelected] = useState<ITeamMember | null>(null);

    useEffect(() => {
        getTeams().then(res => {
            if (res?.data) {
                setTeam(res.data);
            }
        });
    }, []);

    function stripHtml(html: string) {
        if (typeof window === 'undefined') return '';
        const div = document.createElement('div');
        div.innerHTML = html;
        return div.textContent || div.innerText || '';
    }

    function truncateText(text: string, maxLength: number) {
        if (text.length <= maxLength) return text;
        return text.slice(0, maxLength) + '...';
    }

    if (!team) return <div className="text-center py-8">Loading team data...</div>;

    return (
        <section className="py-8 md:py-16 bg-gradient-to-b from-secondary/10 to-secondary/15">
            <div className="container mx-auto px-4">
                <div className="text-center mb-16">
                    <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">{team.title}</h2>
                </div>

                <div className="sm:hidden relative">
                    <Carousel opts={{ align: 'start', loop: true }} className="w-full">
                        <CarouselContent className="-ml-2">
                            {team.members.map((member, i) => (
                                <CarouselItem key={member.id} className="basis-auto pl-3">
                                    <div className="flex justify-center">
                                        <div
                                            className={`group relative w-56 flex-shrink-0 ${i !== 0 ? '-ml-1' : ''}`}
                                            style={{ overflow: 'visible' }}
                                        >
                                            <div
                                                className={`relative z-0 rounded-xl bg-white shadow-md cursor-pointer overflow-hidden ring-2 ring-brand/40 transition-transform duration-300 ease-out group-hover:ring-brand group-hover:scale-105 ${i % 2 === 0 ? 'rotate-1' : '-rotate-1'
                                                    } group-hover:-rotate-1 group-hover:z-20`}
                                                onClick={() =>
                                                    setSelected({
                                                        ...member,
                                                        bio: member.bio,
                                                    })
                                                }
                                            >
                                                <div className="relative w-full h-72 overflow-hidden">
                                                    <Image
                                                        src={member.image}
                                                        alt={member.name}
                                                        fill
                                                        className="w-full h-full object-cover transition-opacity duration-300"
                                                    />
                                                    <div className="absolute top-1/2 right-0 -translate-y-1/2 translate-x-4 pointer-events-none opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-30">
                                                        <span className="bg-white shadow-lg text-secondary font-semibold text-xs px-4 py-2 rounded-full border border-gray-200">
                                                            More about {member.name.split(' ').slice(0, 2).join(' ')}
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </CarouselItem>
                            ))}
                        </CarouselContent>

                        <CarouselPrevious className="left-2" />
                        <CarouselNext className="right-2" />
                    </Carousel>
                </div>

                <div className="hidden sm:flex items-center justify-center overflow-x-auto pb-4 pt-4">
                    {team.members.map((member, i) => (
                        <div
                            key={member.id}
                            className={`group relative w-56 flex-shrink-0 ${i !== 0 ? '-ml-2' : ''}`}
                            style={{ overflow: 'visible' }}
                        >
                            <div
                                className={`relative z-0 rounded-xl bg-white shadow-md cursor-pointer overflow-hidden ring-2 ring-brand/40 transition-transform duration-300 ease-out group-hover:ring-brand group-hover:scale-105 ${i % 2 === 0 ? 'rotate-1' : '-rotate-1'
                                    } group-hover:-rotate-1 group-hover:z-20`}
                                onClick={() =>
                                    setSelected({
                                        ...member,
                                        bio: member.bio,
                                    })
                                }
                            >
                                <div className="relative w-full h-72 overflow-hidden">
                                    <Image
                                        src={member.image}
                                        alt={member.name}
                                        fill
                                        className="w-full h-full object-cover transition-opacity duration-300"
                                    />
                                    <div className="absolute top-1/2 right-0 -translate-y-1/2 translate-x-4 pointer-events-none opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-30">
                                        <span className="bg-white shadow-lg text-secondary font-semibold text-xs px-4 py-2 rounded-full border border-gray-200">
                                            More about {member.name.split(' ').slice(0, 2).join(' ')}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>

                <div className="text-center">
                    <Button className="bg-brand hover:bg-brand/80 text-light px-8 mt-4">
                        <a href="/teams">View All Details</a>
                    </Button>
                </div>

                {selected && (
                    <div className="fixed inset-0 bg-black/40 flex items-center justify-center z-50 p-3">
                        <div className="bg-white rounded-xl shadow-lg p-4 sm:p-6 max-w-3xl w-full relative">
                            <button
                                className="absolute z-10 top-2 right-3 text-black hover:text-primary text-lg sm:text-xl"
                                onClick={() => setSelected(null)}
                                aria-label="Close"
                            >
                                ×
                            </button>

                            <div className="flex flex-col md:flex-row gap-4 sm:gap-6">
                                {/* Image */}
                                <div className="md:w-1/2">
                                    <div className="relative w-full h-56 sm:h-72 md:h-full">
                                        <Image
                                            src={selected.image}
                                            alt={selected.name}
                                            fill
                                            className="object-cover rounded-lg"
                                            sizes="(max-width: 768px) 100vw, 50vw"
                                        />
                                    </div>
                                </div>

                                {/* Details */}
                                <div className="md:w-1/2 flex flex-col justify-center">
                                    <h3 className="text-lg sm:text-2xl font-bold mb-1 sm:mb-2 text-secondary">
                                        {selected.name}
                                    </h3>
                                    <p className="text-xs sm:text-sm font-medium text-gray-700 mb-2 sm:mb-3">
                                        {selected.role}
                                    </p>
                                    <div className="text-gray-600 text-xs sm:text-sm leading-relaxed">
                                        <HtmlContentDisplay
                                            htmlContent={truncateText(stripHtml(selected.bio), 400)}
                                            className="text-justify"
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                )}

            </div>
        </section>
    );
}



// 'use client';

// import { useState, useEffect } from 'react';
// import getTeams from '@/actions/teams/get-all-teams';
// import { Button } from '@/components/ui/button';
// import Image from 'next/image';
// import { ITeam, ITeamMember } from '@/types/team';
// import HtmlContentDisplay from '@/components/html-content-display';

// export default function OurTeamSection() {
//     const [team, setTeam] = useState<null | ITeam>(null);
//     const [selected, setSelected] = useState<ITeamMember | null>(null);

//     useEffect(() => {
//         getTeams().then(res => {
//             if (res?.data) {
//                 setTeam(res.data);
//             }
//         });
//     }, []);

//     function stripHtml(html: string) {
//         if (typeof window === 'undefined') return '';
//         const div = document.createElement('div');
//         div.innerHTML = html;
//         return div.textContent || div.innerText || '';
//     }

//     function truncateText(text: string, maxLength: number) {
//         if (text.length <= maxLength) return text;
//         return text.slice(0, maxLength) + '...';
//     }


//     if (!team) return <div className="text-center py-8">Loading team data...</div>;

//     return (
//         <section className="py-8 md:py-16 bg-gradient-to-b from-secondary/10 to-secondary/15">
//             <div className="container mx-auto px-4">
//                 <div className="text-center mb-16">
//                     <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">{team.title}</h2>
//                 </div>

//                 {/* Members showcase */}
//                 <div className="flex items-center justify-center overflow-x-auto pb-4 pt-4">
//                     {team.members.map((member, i) => (
//                         <div
//                             key={member.id}
//                             className={`group relative w-56 flex-shrink-0 ${i !== 0 ? '-ml-2' : ''}`}
//                             style={{ overflow: 'visible' }}
//                         >
//                             <div
//                                 className={`relative z-0 rounded-xl bg-white shadow-md cursor-pointer overflow-hidden ring-2 ring-brand/40 transition-transform duration-300 ease-out group-hover:ring-brand group-hover:scale-105 ${i % 2 === 0 ? 'rotate-1' : '-rotate-1'
//                                     } group-hover:-rotate-1 group-hover:z-20`}
//                                 onClick={() =>
//                                     setSelected({
//                                         ...member,
//                                         bio: member.bio,
//                                     })
//                                 }
//                             >
//                                 <div className="relative w-full h-72 overflow-hidden">
//                                     <Image
//                                         src={member.image}
//                                         alt={member.name}
//                                         fill
//                                         className="w-full h-full object-cover transition-opacity duration-300"
//                                     />
//                                     <div className="absolute top-1/2 right-0 -translate-y-1/2 translate-x-4 pointer-events-none opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-30">
//                                         <span className="bg-white shadow-lg text-secondary font-semibold text-xs px-4 py-2 rounded-full border border-gray-200">
//                                             More about {member.name.split(' ').slice(0, 2).join(' ')}
//                                         </span>
//                                     </div>
//                                 </div>
//                             </div>
//                         </div>
//                     ))}
//                 </div>

//                 <div className="text-center">
//                     <Button className="bg-brand hover:bg-brand/80 text-light px-8 mt-4">
//                         <a href="/teams">View All Details</a>
//                     </Button>
//                 </div>

//                 {selected && (
//                     <div className="fixed inset-0 bg-black/40 flex items-center justify-center z-50">
//                         <div className="bg-white rounded-xl shadow-lg p-6 max-w-3xl w-full relative">
//                             <button
//                                 className="absolute top-3 right-3 text-gray-500 hover:text-primary text-lg"
//                                 onClick={() => setSelected(null)}
//                                 aria-label="Close"
//                             >
//                                 ×
//                             </button>

//                             <div className="flex flex-col md:flex-row gap-6">
//                                 {/* Image */}
//                                 <div className="md:w-1/2">
//                                     <Image
//                                         src={selected.image}
//                                         alt={selected.name}
//                                         width={400}
//                                         height={400}
//                                         className="w-full h-64 md:h-full object-cover rounded-xl"
//                                     />
//                                 </div>
//                                 {/* Details */}
//                                 <div className="md:w-1/2 flex flex-col justify-center">
//                                     <h3 className="text-2xl font-bold mb-2 text-secondary">{selected.name}</h3>
//                                     <p className="text-sm font-medium text-gray-700 mb-3">{selected.role}</p>
//                                     <div className="text-gray-600 mb-4">
//                                         <HtmlContentDisplay
//                                             htmlContent={truncateText(stripHtml(selected.bio), 400)}
//                                             className="text-justify"
//                                         />
//                                     </div>
//                                 </div>
//                             </div>
//                         </div>
//                     </div>
//                 )}
//             </div>
//         </section>
//     );
// }


// 'use client';

// import getTeams from '@/actions/teams/get-all-teams';
// import { Button } from '@/components/ui/button';
// import { ITeam, ITeamMember } from '@/types/team';
// import Image from 'next/image';
// import Link from 'next/link';
// import { useState } from 'react';
// import HtmlContentDisplay from '@/components/html-content-display';

// export default async function OurTeamSection() {
//     const res = await getTeams();
//     const team: ITeam[] = (res?.data ?? [])

//     const [selected, setSelected] = useState<ITeam | null>(null);

//     return (
//         <section className="py-8 md:py-16 bg-gradient-to-b from-secondary/2 to-secondary/15">
//             <div className="container mx-auto px-4">
//                 <div className="text-center mb-16">
//                     <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
//                         Meet the Team
//                     </h2>
//                 </div>
//                 {/* <h2 className="text-3xl flex items-center justify-center font-extrabold mb-8 text-secondary">Meet the Team</h2> */}
//                 <div className="flex items-center justify-center overflow-x-auto pb-4 pt-4">
//                     {posts.map((member, i) => (
//                         <div
//                             key={i}
//                             className={`group relative w-56 flex-shrink-0 ${i !== 0 ? '-ml-6' : ''}  `}
//                             style={{ overflow: 'visible' }}
//                         >
//                             <div
//                                 className={`relative z-0 rounded-xl bg-white shadow-md cursor-pointer overflow-hidden ring-2 ring-brand/40 transition-transform duration-300 ease-out group-hover:ring-brand group-hover:scale-105 ${i % 2 === 0 ? 'rotate-1' : '-rotate-1'} group-hover:-rotate-1 group-hover:z-20 `}
//                                 onClick={() => setSelected(member)}
//                             >
//                                 <div className="relative w-full h-72 overflow-hidden">
//                                     <Image
//                                         src={member.image}
//                                         alt={member.name}
//                                         fill
//                                         className="w-full h-full object-cover transition-opacity duration-300"
//                                     />

//                                     <div
//                                         className={`absolute top-1/2 right-0 -translate-y-1/2 translate-x-4 pointer-events-none opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-30 `}
//                                     >
//                                         <span className=" bg-white shadow-lg text-secondary font-semibold text-xs px-4 py-2 rounded-full border  border-gray-200 ">
//                                             More about {member.name.split(' ').slice(0, 2).join(' ')}
//                                         </span>
//                                     </div>
//                                 </div>

//                             </div>
//                         </div>
//                     ))}
//                 </div>

//                 <div className="text-center">
//                     <Button
//                         className="bg-brand hover:bg-brand/80 text-light px-8 mt-4"
//                     >
//                         <Link
//                             href="/teams"
//                         >
//                             View All
//                         </Link>
//                     </Button>
//                 </div>

//                 {selected && (
//                     <div className="fixed inset-0 bg-black/40 flex items-center justify-center z-50">
//                         <div className="bg-white rounded-xl shadow-lg p-6 max-w-3xl w-full relative">

//                             {/* Close Button */}
//                             <button
//                                 className="absolute top-3 right-3 text-gray-500 hover:text-primary text-lg"
//                                 onClick={() => setSelected(null)}
//                                 aria-label="Close"
//                             >
//                                 ×
//                             </button>

//                             {/* Side-by-side layout */}
//                             <div className="flex flex-col md:flex-row gap-6">

//                                 {/* Left: Video or Photo */}
//                                 <div className="md:w-1/2">
//                                     <Image
//                                         src={selected.photo}
//                                         alt={selected.name}
//                                         width={400}
//                                         height={400}
//                                         className="w-full h-64 md:h-full object-cover rounded-xl"
//                                     />
//                                 </div>

//                                 {/* Right: Content */}
//                                 <div className="md:w-1/2 flex flex-col justify-center">
//                                     <h3 className="text-2xl font-bold mb-2 text-secondary">{selected.name}</h3>
//                                     <p className="text-sm text-gray-700 mb-3">{selected.title}</p>
//                                     <p className="text-gray-600 mb-4">{selected.bio}</p>
//                                     <div className="flex flex-wrap gap-2">
//                                         {selected.tags.map(tag => (
//                                             <span
//                                                 key={tag}
//                                                 className="px-3 py-1 rounded-full bg-primary/10 text-primary text-xs font-medium"
//                                             >
//                                                 {tag}
//                                             </span>
//                                         ))}
//                                     </div>
//                                 </div>
//                             </div>
//                         </div>
//                     </div>
//                 )}

//             </div>
//         </section >
//     );
// }