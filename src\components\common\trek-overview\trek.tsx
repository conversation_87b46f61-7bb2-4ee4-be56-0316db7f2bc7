import React from 'react';
import Image from 'next/image';

interface TrekData {
    destination?: string;
    accommodation?: string;
    duration?: string;
    maxElevation?: string;
    group?: string;
    region?: string;
    transport?: string;
    activityPerDay?: string;
    mealsIncluded?: string;
    bestSeason?: string;
    grade?: string;
    type?: string;
}

interface TrekInfoProps {
    trekData?: TrekData;
}

const TrekInfo: React.FC<TrekInfoProps> = ({ trekData }) => {
    const defaultData = {
        accommodation: 'Guest House/ Lodge/camping',
        mealsIncluded: '(Breakfast, Lunch, and Dinner) during the trek',
        bestSeason: 'March to May and September to November',
        grade: 'Challenging'
    };

    const data = { ...defaultData, ...trekData };

    const trekDetails = [
        {
            icon: '/images/new-icon/12.png',
            title: 'Start/End',
            value: data.destination,
            iconAlt: 'Destination icon'
        },
        {
            icon: '/images/new-icon/icon-tent-green.png',
            title: 'Accommodation',
            value: data.accommodation,
            iconAlt: 'Accommodation icon'
        },
        {
            icon: '/images/new-icon/3.png',
            title: 'Trail Type',
            value: data.type,
            iconAlt: 'Type icon'
        },
        {
            icon: '/images/new-icon/cloud_green.png',
            title: 'Best Season',
            value: data.bestSeason,
            iconAlt: 'Season icon'
        },
        {
            icon: '/images/new-icon/9.png',
            title: 'Grade',
            value: data.grade,
            iconAlt: 'Grade icon'
        },
        {
            icon: '/images/new-icon/11.png',
            title: 'Duration',
            value: data.duration,
            iconAlt: 'Duration icon'
        },
        {
            icon: '/images/new-icon/icons_.png',
            title: 'Activity per day',
            value: data.activityPerDay,
            iconAlt: 'Elevation icon'
        },
        
    ];

    return (
        <div className="bg-light rounded-lg items-center ">
            <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {trekDetails.map((detail, index) => (
                    <div key={index} className="flex flex-col space-y-1">
                        <div className="flex flex-col text-center space-x-3">
                            <div className="w-12 h-12 relative flex justify-center items-center mx-auto">
                                <Image
                                    src={detail.icon}
                                    alt={detail.iconAlt}
                                    width={32}
                                    height={32}
                                    className="w-full h-full object-center object-contain"
                                />
                            </div>
                            <h3 className="text-dark text-sm tracking-wide mt-2">
                                {detail.title}
                            </h3>
                            <p className="text-sm text-dark/80 leading-relaxed">
                                {detail.value}
                            </p>
                        </div>


                    </div>
                ))}
            </div>
        </div>
    );
};

export default TrekInfo;