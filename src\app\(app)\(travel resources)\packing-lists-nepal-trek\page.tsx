import React from "react";

export default function TrailRunningPackingList() {
  return (
    <main className="max-w-5xl mx-auto p-6 bg-white text-gray-900 font-sans">
      <h1 className="text-4xl font-bold mb-6">
        Essential Trail Running Packing List for Nepal: Gear Up for Himalayan Heights
      </h1>

      <p className="mb-6">
        Your trail running packing list can transform your Nepal adventure from challenging to extraordinary. Whether you're tackling Pokhara's forested ridges or pushing toward Annapurna Base Camp, having the right gear ensures you run faster, safer, and more comfortably through the Himalayas. This comprehensive guide covers everything trail runners need for Nepal's diverse terrain and altitude challenges.
      </p>

      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-4">
          Trail Running Clothing: Light, Fast, Functional
        </h2>
        <p className="mb-4">
          Trail running in Nepal demands clothing that performs across extreme temperature variations and intense physical output. Every layer must earn its place in your pack through versatility and weight efficiency.
        </p>
      </section>

      <section className="mb-8">
        <h3 className="text-xl font-semibold mb-3">Base Layers for Mountain Running</h3>
        <p className="mb-3">
          Moisture-wicking base layers form your foundation for high-altitude trail running. Choose merino wool or technical synthetic materials that manage sweat efficiently while providing light insulation. Unlike trekking, trail running generates significant body heat, so prioritize breathability over warmth.
        </p>
        <p className="font-semibold mb-1">Pack:</p>
        <ul className="list-disc list-inside mb-6 space-y-1">
          <li>2-3 lightweight, moisture-wicking running t-shirts (short and long sleeve)</li>
          <li>1-2 technical running base layer tops for cold weather</li>
          <li>2 pairs running underwear (anti-chafe, quick-dry)</li>
          <li>1-2 technical running tights or leggings for high altitude</li>
        </ul>

        <h3 className="text-xl font-semibold mb-3">Insulating Layers for Trail Runners</h3>
        <p className="mb-3">
          When you stop moving at altitude, temperatures plummet. Your insulation needs to pack small, weigh little, and warm quickly. Down or synthetic insulated jackets designed for mountain athletes deliver the best warmth-to-weight ratio.
        </p>
        <p className="font-semibold mb-1">Pack:</p>
        <ul className="list-disc list-inside mb-6 space-y-1">
          <li>1 ultralight down jacket or synthetic puffy (compressible)</li>
          <li>1 lightweight fleece midlayer (optional for extended trips)</li>
        </ul>

        <h3 className="text-xl font-semibold mb-3">Outer Layers: Weather Protection</h3>
        <p className="mb-3">
          Nepal's weather changes rapidly, especially above 3,000 meters. Your shell layers protect against wind, rain, and snow while remaining breathable enough for high-intensity running. Skip heavy trekking shells in favor of running-specific outerwear.
        </p>
        <p className="font-semibold mb-1">Pack:</p>
        <ul className="list-disc list-inside mb-6 space-y-1">
          <li>1 lightweight, packable waterproof running jacket (breathable fabric)</li>
          <li>1 pair ultralight waterproof pants or running rain pants</li>
          <li>1 windproof running vest (optional but valuable)</li>
        </ul>
      </section>

      <section className="mb-8">
        <h3 className="text-xl font-semibold mb-3">Trail Running Bottoms</h3>
        <p className="mb-3">
          Trail running shorts and pants must balance durability with minimal weight. Look for designs with secure pockets for gels, phones, and essentials. Many runners prefer shorts with built-in liners or compression layers underneath.
        </p>
        <p className="font-semibold mb-1">Pack:</p>
        <ul className="list-disc list-inside mb-6 space-y-1">
          <li>2-3 trail running shorts (various lengths)</li>
          <li>1-2 running tights or convertible pants for cold/high altitude</li>
          <li>1 compression shorts or base layer bottoms</li>
        </ul>

        <h3 className="text-xl font-semibold mb-3">Technical Running Socks</h3>
        <p className="mb-3">
          Quality trail running socks prevent blisters and manage moisture during long mountain efforts. Merino wool blends offer the best performance across temperature ranges, providing cushioning without bulk.
        </p>
        <p className="font-semibold mb-1">Pack:</p>
        <ul className="list-disc list-inside mb-6 space-y-1">
          <li>3-4 pairs trail running socks (merino wool or technical synthetic)</li>
          <li>1-2 pairs lightweight liner socks (for blister prevention)</li>
        </ul>
      </section>

      <section className="mb-8">
        <h3 className="text-xl font-semibold mb-3">Extremities: Hands and Head</h3>
        <p className="mb-3">
          High-altitude Himalayan running exposes you to intense sun and cold simultaneously. Protect extremities without compromising dexterity or temperature regulation.
        </p>
        <p className="font-semibold mb-1">Pack:</p>
        <ul className="list-disc list-inside mb-6 space-y-1">
          <li>1 lightweight running cap or visor</li>
          <li>1 warm beanie or headband for high altitude</li>
          <li>1 buff or neck gaiter (versatile for sun/cold protection)</li>
          <li>1 pair lightweight running gloves</li>
          <li>1 pair insulated gloves for high altitude/winter</li>
          <li>1 pair high-quality trail running sunglasses (polarized, UV400)</li>
        </ul>
      </section>

      <section className="mb-8">
        <h3 className="text-xl font-semibold mb-3">Trail Running Footwear: Where Rubber Meets Rock</h3>
        <p className="mb-3">
          Footwear represents your most critical gear choice for Nepal trail running. The terrain ranges from smooth lakeside paths to technical high-altitude trails with loose rock, mud, and occasional snow.
        </p>

        <h4 className="text-lg font-semibold mb-2">Trail Running Shoes</h4>
        <p className="mb-3">
          Invest in quality trail runners with aggressive tread, rock plates, and good drainage. For Nepal's varied terrain, shoes with moderate cushioning and stability features perform best. Break them in thoroughly before your trip.
        </p>
        <p className="font-semibold mb-1">Pack:</p>
        <ul className="list-disc list-inside mb-6 space-y-1">
          <li>1 pair primary trail running shoes (aggressive lugs, protective toe cap)</li>
          <li>1 pair backup trail runners or approach shoes (different model for variety)</li>
        </ul>
        <p className="font-semibold mb-1">Key Features to Prioritize:</p>
        <ul className="list-disc list-inside mb-6 space-y-1">
          <li>Vibram or comparable aggressive outsole</li>
          <li>Water-resistant or quick-dry upper</li>
          <li>Rock plate protection</li>
          <li>Secure lacing system</li>
          <li>Good ankle support without restricting mobility</li>
        </ul>

        <h4 className="text-lg font-semibold mb-2">Recovery Footwear</h4>
        <p className="mb-3">
          After hours on the trail, your feet need relief. Lightweight recovery shoes or sandals allow swelling and provide comfort around lodges and teahouses.
        </p>
        <p className="font-semibold mb-1">Pack:</p>
        <ul className="list-disc list-inside mb-6 space-y-1">
          <li>1 pair recovery sandals or camp shoes (minimal weight)</li>
        </ul>

        <h4 className="text-lg font-semibold mb-2">Traction Accessories</h4>
        <p className="mb-3">
          For high-altitude routes with snow and ice (Thorong La Pass, Larkya La, etc.), traction devices prevent slips and increase confidence.
        </p>
        <p className="font-semibold mb-1">Pack:</p>
        <ul className="list-disc list-inside mb-6 space-y-1">
          <li>1 pair microspikes or lightweight trail crampons (for passes above 4,500m)</li>
          <li>1 pair lightweight gaiters (optional, for debris and snow)</li>
        </ul>
      </section>

      {/* More sections can be added similarly for remaining content */}

      <section className="mb-6">
        <h2 className="text-2xl font-semibold mb-4">
          Conclusion: Run Light, Run Far
        </h2>
        <p>
          Trail running in Nepal rewards those who pack smart—carrying just enough to stay safe and comfortable while maintaining the speed and freedom that makes running magical. Every item in your pack should justify its weight through function, versatility, or safety value.
          Whether you're running Pokhara's sunrise trails or pushing toward Himalayan base camps, the right gear transforms challenges into adventure and obstacles into opportunities. Test everything, pack thoughtfully, and embrace the incredible trail running experiences waiting in Nepal's mountains.
          The trails are calling. Your perfectly-packed vest is ready. Time to run where the earth touches the sky.
          Ready to tackle Nepal's trails? Use this comprehensive packing list to prepare for the mountain running adventure of a lifetime.
        </p>
      </section>
    </main>
  );
}


// import React from "react";
// import {
//   Backpack,
//   Mountain,
//   Sun,
//   CloudRain,
//   Zap,
//   Droplet,
//   Umbrella,
//   Footprints,
//   Thermometer,
//   UserCircle2,
//   ListChecks
// } from "lucide-react";

// // You can further split the content into subcomponents and import icons as desired.

// interface ListTagProps {
//   children: React.ReactNode;
//   color: string;
// }

// const ListTag = ({ children, color }: ListTagProps) => (
//   <span className={`inline-block px-2 py-0.5 rounded text-xs font-semibold ${color} mr-2 mb-1`}>
//     {children}
//   </span>
// );

// interface SectionHeadingProps {
//   icon: React.ReactNode;
//   title: string;
// }

// const SectionHeading = ({ icon, title }: SectionHeadingProps) => (
//   <div className="flex items-center gap-2 mb-3 mt-8">
//     {icon}
//     <h2 className="text-xl md:text-2xl font-bold text-gray-900">{title}</h2>
//   </div>
// );

// const PACKING_CATEGORIES = [
//   {
//     heading: <SectionHeading icon={<Backpack className="w-6 h-6 text-blue-600" />} title="Trail Running Clothing" />,
//     description: "Light, fast, functional layers for Nepal’s altitude and weather.",
//     items: [
//       <span>2-3 moisture-wicking running shirts <ListTag color="bg-blue-100 text-blue-700">Base</ListTag></span>,
//       <span>1-2 tech running base tops <ListTag color="bg-gray-100 text-gray-700">Cold</ListTag></span>,
//       <span>2 running underwear <ListTag color="bg-green-100 text-green-700">Essential</ListTag></span>,
//       <span>1-2 running tights/leggings <ListTag color="bg-gray-200 text-gray-800">High Alt</ListTag></span>,
//       <span>1 ultralight down/synthetic jacket <ListTag color="bg-blue-100 text-blue-700">Insulation</ListTag></span>,
//       <span>1 waterproof running jacket <ListTag color="bg-blue-300 text-blue-900">Shell</ListTag></span>,
//       <span>1-2 waterproof pants/vests <ListTag color="bg-blue-100 text-blue-700">Weather</ListTag></span>
//     ]
//   },
//   {
//     heading: <SectionHeading icon={<Footprints className="w-6 h-6 text-orange-700" />} title="Trail Running Footwear" />,
//     description: "Shoes and accessories for Nepal’s rugged, rocky paths.",
//     items: [
//       <span>Trail running shoes <ListTag color="bg-orange-100 text-orange-700">Key</ListTag></span>,
//       <span>Backup shoes/approach shoes</span>,
//       <span>Recovery sandals/camp shoes</span>,
//       <span>Microspikes/trail crampons <ListTag color="bg-blue-100 text-blue-700">High passes</ListTag></span>,
//       <span>Liner or merino socks (4-6 pairs)</span>,
//       <span>Gaiters (optional for snow/debris)</span>
//     ]
//   },
//   {
//     heading: <SectionHeading icon={<Mountain className="w-6 h-6 text-green-700" />} title="Running Systems & Essentials" />,
//     description: "Packs, hydration, poles and safety gear for self-supported runs.",
//     items: [
//       <span>Running pack/vest (8-15L)</span>,
//       <span>Soft flasks/bladder (2-3L total)</span>,
//       <span>Collapsible bottle (backup)</span>,
//       <span>Purification tablets or SteriPEN</span>,
//       <span>Trail running poles (collapsible)</span>,
//       <span>GPS watch, smartphone (offline maps)</span>,
//       <span>Powerbank 10,000+ mAh, cables, headlamp</span>,
//       <span>Compact first aid kit, whistle</span>
//     ]
//   },
//   {
//     heading: <SectionHeading icon={<ListChecks className="w-6 h-6 text-fuchsia-700" />} title="Nutrition & Recovery" />,
//     description: "Fast energy, personal meds and altitude essentials.",
//     items: [
//       <span>12-20 energy gels, chews, bars</span>,
//       <span>Trail mix/nuts/dried fruit/ORS</span>,
//       <span>Protein/recovery mix (optional)</span>,
//       <span>Ibuprofen/acetazolamide/elastic bandage/blister kit</span>
//     ]
//   },
//   {
//     heading: <SectionHeading icon={<UserCircle2 className="w-6 h-6 text-gray-700" />} title="Personal Care & Documents" />,
//     description: "Hygiene, sun shields, permits, and backup tech.",
//     items: [
//       <span>Biodegradable soap, toothbrush, wet wipes</span>,
//       <span>Sunscreen SPF 50+, lip balm, anti-chafe balm</span>,
//       <span>Small towel, sanitizer, TP, earplugs</span>,
//       <span>Passport, permit, cash, insurance docs</span>
//     ]
//   }
// ];

// const TrailRunningPackingPage = () => (
//   <div className="bg-gray-50 min-h-screen py-10">
//     {/* Hero */}
//     <div className="container mx-auto bg-gradient-to-r from-brand to-secondary/50 text-white rounded-2xl px-6 py-10 mb-10 text-center shadow-lg">
//       <h1 className="text-3xl md:text-4xl font-bold mb-2">Essential Trail Running Packing List for Nepal</h1>
//       <p className="text-lg mb-4">
//         Gear up for Himalayan heights: the only checklist you'll need for speed, safety, and comfort from Pokhara Ridge to Annapurna Base Camp.
//       </p>
//       <div className="flex flex-wrap gap-2 justify-center mt-2">
//         <ListTag color="bg-blue-100 text-blue-700">Nepal</ListTag>
//         <ListTag color="bg-green-100 text-green-700">Himalayas</ListTag>
//         <ListTag color="bg-orange-100 text-orange-700">Trail Running</ListTag>
//         <ListTag color="bg-pink-100 text-pink-700">Packing</ListTag>
//       </div>
//     </div>

//     <div className="container mx-auto space-y-12">
//       {/* Gear Categories */}
//       {PACKING_CATEGORIES.map(({ heading, description, items }, i) => (
//         <section className="rounded-2xl bg-white/90 shadow p-6" key={i}>
//           {heading}
//           <div className="text-gray-600 mb-2">{description}</div>
//           <ul className="list-disc ml-6 mt-2 space-y-2">
//             {items.map((item, j) => (
//               <li key={j} className="leading-relaxed">{item}</li>
//             ))}
//           </ul>
//         </section>
//       ))}

//       {/* Seasonal Gear Section */}
//       <section className="rounded-2xl bg-blue-50 p-6">
//         <SectionHeading icon={<Sun className="w-5 h-5 text-yellow-400" />} title="Seasonal Gear Tips" />
//         <ul className="ml-6 list-disc space-y-2 mb-2">
//           <li><b>Spring</b>: Layer for cold nights, sun cap, ultralight jacket</li>
//           <li><b>Monsoon</b>: Waterproof shoes, extra socks, rain shell, minimum clothing</li>
//           <li><b>Autumn</b>: Versatile layering, light insulation, sun protection</li>
//           <li><b>Winter</b>: Heavy jacket, insulated gloves/hat, thermal tights, microspikes</li>
//         </ul>
//       </section>

//       {/* Final Checklist */}
//       <section className="rounded-2xl bg-green-50 p-6">
//         <SectionHeading icon={<ListChecks className="w-5 h-5 text-green-600" />} title="Final Packing Checklist" />
//         <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
//           <ul className="list-disc ml-5 space-y-1">
//             <li>Clothing (layers/tights/hat/gloves)</li>
//             <li>Trail shoes & backup</li>
//             <li>Running pack & hydration</li>
//             <li>Poles, GPS, headlamp</li>
//             <li>Snacks, energy, electrolytes</li>
//             <li>First aid/meds/altitude kit</li>
//             <li>Personal docs & hygiene</li>
//           </ul>
//           <ul className="list-disc ml-5 space-y-1">
//             <li>Socks & underwear</li>
//             <li>Recovery sandals</li>
//             <li>Microspikes/gaiters (if needed)</li>
//             <li>Powerbank/charger/cables</li>
//             <li>Sunscreen/lip balm/antichafe</li>
//             <li>Toiletries/wipes/towel</li>
//             <li>Money/passport/permits</li>
//           </ul>
//         </div>
//       </section>
//     </div>

//     {/* Outro CTA */}
//     <div className="container mx-auto mt-10 text-center">
//       <div className="rounded-xl bg-gradient-to-r from-green-400 to-blue-400 p-6 text-white font-bold shadow-md text-lg">
//         The trails are calling. Your perfectly-packed vest is ready. Run where the earth touches the sky.
//       </div>
//     </div>
//   </div>
// );

// export default TrailRunningPackingPage;
